<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>在线客服</title>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/layui@2.6.8/css/layui.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-light: #764ba2;
            --secondary-color: #f093fb;
            --secondary-light: #f5576c;
            --success-color: #4facfe;
            --success-light: #00f2fe;
            --warning-color: #fa709a;
            --warning-light: #fee140;
            --error-color: #ff6b6b;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --text-light: #bdc3c7;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --border-radius: 16px;
            --border-radius-sm: 8px;
            --border-radius-lg: 24px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --box-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: var(--text-primary);
            line-height: 1.6;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
        }

        .header {
            background: var(--bg-gradient);
            height: 64px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            position: relative;
            box-shadow: var(--box-shadow);
            flex-shrink: 0;
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: -1;
        }

        .header a.back {
            position: absolute;
            left: 20px;
            text-decoration: none;
            color: #fff;
            font-size: 20px;
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.1);
        }

        .header a.back:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-2px);
        }

        .header h4 {
            font-weight: 600;
            font-size: 20px;
            letter-spacing: 0.5px;
        }
        
        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 500px;
            margin: 0 auto;
            width: 100%;
            padding: 0 16px;
            gap: 20px;
        }

        .contact-card {
            background: var(--bg-primary);
            padding: 24px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            flex-shrink: 0;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: var(--transition);
        }

        .contact-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--box-shadow-hover);
        }

        .contact-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            padding: 4px 0;
            transition: var(--transition);
        }

        .contact-header:hover {
            background: rgba(102, 126, 234, 0.05);
            border-radius: var(--border-radius-sm);
            margin: 0 -8px;
            padding: 8px;
        }

        .contact-title {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .contact-icon-main {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(250, 112, 154, 0.3);
        }

        .contact-main-label {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .contact-main-desc {
            font-size: 13px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .contact-toggle {
            padding: 8px;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.1);
            transition: var(--transition);
        }

        .contact-toggle:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: scale(1.1);
        }

        .toggle-icon {
            font-size: 14px;
            color: var(--primary-color);
            font-weight: 600;
            transition: var(--transition);
        }

        .contact-toggle.expanded .toggle-icon {
            transform: rotate(180deg);
        }

        .contact-methods {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(0, 0, 0, 0.06);
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .qq-icon {
            background: linear-gradient(135deg, #12B7F5 0%, #0084FF 100%);
        }

        .wechat-icon {
            background: linear-gradient(135deg, #07C160 0%, #00D100 100%);
        }

        .telegram-icon {
            background: linear-gradient(135deg, #0088CC 0%, #229ED9 100%);
        }

        .email-icon {
            background: linear-gradient(135deg, #EA4335 0%, #FBBC04 100%);
        }

        .phone-icon {
            background: linear-gradient(135deg, #34A853 0%, #4285F4 100%);
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            transition: var(--transition);
            border-radius: var(--border-radius-sm);
            margin: 0 -8px;
            padding-left: 8px;
            padding-right: 8px;
        }

        .contact-item:last-child {
            border-bottom: none;
        }

        .contact-item:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(4px);
        }

        .contact-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            margin-right: 16px;
            font-size: 20px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            transition: var(--transition);
        }

        .contact-icon:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .contact-info {
            flex: 1;
        }
        
        .contact-label {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .contact-value {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .contact-action {
            padding: 8px 16px;
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
            color: #fff;
            border: none;
            border-radius: var(--border-radius-lg);
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: var(--transition);
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
        }

        .contact-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(79, 172, 254, 0.4);
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }

        .chat-header {
            padding: 20px;
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
            color: #fff;
            text-align: center;
            font-weight: 600;
            font-size: 16px;
            position: relative;
        }

        .chat-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: -1;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 400px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            margin: 0 12px;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .message.admin .message-avatar {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: var(--border-radius);
            background: var(--bg-primary);
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: #fff;
            border: none;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .message-time {
            font-size: 11px;
            color: var(--text-light);
            margin-top: 6px;
            text-align: center;
            font-weight: 500;
        }

        .message.user .message-time {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .message-image {
            max-width: 200px;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .chat-input {
            padding: 20px;
            background: var(--bg-primary);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .input-group {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-text {
            flex: 1;
            min-height: 44px;
            max-height: 100px;
            padding: 12px 16px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: var(--border-radius-lg);
            resize: none;
            outline: none;
            font-size: 14px;
            line-height: 1.4;
            background: var(--bg-primary);
            transition: var(--transition);
            font-family: inherit;
        }

        .input-text:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            min-width: 44px;
            height: 44px;
            border: none;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);
            color: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);
            padding: 0 16px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(250, 112, 154, 0.4);
        }

        .action-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 表情包面板样式 */
        .emoji-panel {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow-hover);
            border: 1px solid rgba(0, 0, 0, 0.1);
            max-height: 300px;
            overflow: hidden;
            z-index: 1000;
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .emoji-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            background: var(--bg-secondary);
        }

        .emoji-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .emoji-close {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: var(--transition);
        }

        .emoji-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: var(--text-primary);
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 4px;
            padding: 12px;
            max-height: 240px;
            overflow-y: auto;
        }

        .emoji-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
        }

        .emoji-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.2);
        }

        /* 输入工具样式 */
        .input-tools {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .tool-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: var(--border-radius-sm);
            background: rgba(102, 126, 234, 0.1);
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: var(--transition);
        }

        .tool-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: scale(1.05);
        }

        .tool-btn:active {
            transform: scale(0.95);
        }

        /* 图片消息样式 */
        .message-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            transition: var(--transition);
        }

        .message-image:hover {
            transform: scale(1.02);
        }

        .image-upload-preview {
            position: relative;
            display: inline-block;
            margin: 8px 0;
        }

        .image-preview {
            max-width: 150px;
            max-height: 150px;
            border-radius: var(--border-radius-sm);
            border: 2px solid rgba(102, 126, 234, 0.2);
        }

        .image-remove {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--error-color);
            color: #fff;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: #fff;
            font-size: 12px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .no-messages {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        
        .quick-replies {
            padding: 16px 20px;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, #e9ecef 100%);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .quick-reply {
            padding: 8px 16px;
            background: var(--bg-primary);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: var(--border-radius-lg);
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            color: var(--text-primary);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .quick-reply:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            color: #fff;
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="javascript:window.history.go(-1)" class="back">〈 返回</a>
        <h4>在线客服</h4>
    </div>

    <div class="container">
        <!-- 联系方式 -->
        <div class="contact-card">
            <div class="contact-header" onclick="toggleContactMethods()">
                <div class="contact-title">
                    <div class="contact-icon-main">📞</div>
                    <div>
                        <div class="contact-main-label">多渠道联系方式</div>
                        <div class="contact-main-desc">点击展开查看更多联系方式</div>
                    </div>
                </div>
                <div class="contact-toggle" id="contactToggle">
                    <span class="toggle-icon">▼</span>
                </div>
            </div>

            <div class="contact-methods" id="contactMethods" style="display: none;">
                {+if $contact_info.qq+}
                <div class="contact-item">
                    <div class="contact-icon qq-icon">Q</div>
                    <div class="contact-info">
                        <div class="contact-label">QQ客服</div>
                        <div class="contact-value">{+$contact_info.qq+}</div>
                    </div>
                    <a href="tencent://message/?uin={+$contact_info.qq+}" class="contact-action">联系</a>
                </div>
                {+/if+}

                {+if $contact_info.wechat+}
                <div class="contact-item">
                    <div class="contact-icon wechat-icon">微</div>
                    <div class="contact-info">
                        <div class="contact-label">微信客服</div>
                        <div class="contact-value">{+$contact_info.wechat+}</div>
                    </div>
                    <button class="contact-action" onclick="copyText('{+$contact_info.wechat+}')">复制</button>
                </div>
                {+/if+}

                {+if $contact_info.telegram+}
                <div class="contact-item">
                    <div class="contact-icon telegram-icon">T</div>
                    <div class="contact-info">
                        <div class="contact-label">Telegram</div>
                        <div class="contact-value">{+$contact_info.telegram+}</div>
                    </div>
                    <a href="https://t.me/{+$contact_info.telegram+}" class="contact-action" target="_blank">联系</a>
                </div>
                {+/if+}

                {+if $contact_info.email+}
                <div class="contact-item">
                    <div class="contact-icon email-icon">@</div>
                    <div class="contact-info">
                        <div class="contact-label">邮箱客服</div>
                        <div class="contact-value">{+$contact_info.email+}</div>
                    </div>
                    <a href="mailto:{+$contact_info.email+}" class="contact-action">发邮件</a>
                </div>
                {+/if+}

                {+if $contact_info.phone+}
                <div class="contact-item">
                    <div class="contact-icon phone-icon">📞</div>
                    <div class="contact-info">
                        <div class="contact-label">客服电话</div>
                        <div class="contact-value">{+$contact_info.phone+}</div>
                    </div>
                    <a href="tel:{+$contact_info.phone+}" class="contact-action">拨打</a>
                </div>
                {+/if+}
            </div>
        </div>
        
        <!-- 在线聊天 -->
        <div class="chat-container">
            <div class="chat-header">
                在线客服聊天
                {+if $unread_count > 0+}
                <span style="background: var(--error-color); color: #fff; padding: 2px 6px; border-radius: 10px; font-size: 12px; margin-left: 10px;">
                    {+$unread_count+}条未读
                </span>
                {+/if+}
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="loading">加载中...</div>
            </div>
            
            <div class="quick-replies">
                <div class="quick-reply" onclick="sendQuickReply('充值问题')">充值问题</div>
                <div class="quick-reply" onclick="sendQuickReply('提现问题')">提现问题</div>
                <div class="quick-reply" onclick="sendQuickReply('账户问题')">账户问题</div>
                <div class="quick-reply" onclick="sendQuickReply('其他问题')">其他问题</div>
            </div>
            
            <div class="chat-input">
                <!-- 表情包面板 -->
                <div class="emoji-panel" id="emojiPanel" style="display: none;">
                    <div class="emoji-header">
                        <span class="emoji-title">选择表情</span>
                        <button class="emoji-close" onclick="toggleEmojiPanel()">×</button>
                    </div>
                    <div class="emoji-grid">
                        <span class="emoji-item" onclick="insertEmoji('😀')">😀</span>
                        <span class="emoji-item" onclick="insertEmoji('😃')">😃</span>
                        <span class="emoji-item" onclick="insertEmoji('😄')">😄</span>
                        <span class="emoji-item" onclick="insertEmoji('😁')">😁</span>
                        <span class="emoji-item" onclick="insertEmoji('😆')">😆</span>
                        <span class="emoji-item" onclick="insertEmoji('😅')">😅</span>
                        <span class="emoji-item" onclick="insertEmoji('😂')">😂</span>
                        <span class="emoji-item" onclick="insertEmoji('🤣')">🤣</span>
                        <span class="emoji-item" onclick="insertEmoji('😊')">😊</span>
                        <span class="emoji-item" onclick="insertEmoji('😇')">😇</span>
                        <span class="emoji-item" onclick="insertEmoji('🙂')">🙂</span>
                        <span class="emoji-item" onclick="insertEmoji('🙃')">🙃</span>
                        <span class="emoji-item" onclick="insertEmoji('😉')">😉</span>
                        <span class="emoji-item" onclick="insertEmoji('😌')">😌</span>
                        <span class="emoji-item" onclick="insertEmoji('😍')">😍</span>
                        <span class="emoji-item" onclick="insertEmoji('🥰')">🥰</span>
                        <span class="emoji-item" onclick="insertEmoji('😘')">😘</span>
                        <span class="emoji-item" onclick="insertEmoji('😗')">😗</span>
                        <span class="emoji-item" onclick="insertEmoji('😙')">😙</span>
                        <span class="emoji-item" onclick="insertEmoji('😚')">😚</span>
                        <span class="emoji-item" onclick="insertEmoji('😋')">😋</span>
                        <span class="emoji-item" onclick="insertEmoji('😛')">😛</span>
                        <span class="emoji-item" onclick="insertEmoji('😝')">😝</span>
                        <span class="emoji-item" onclick="insertEmoji('😜')">😜</span>
                        <span class="emoji-item" onclick="insertEmoji('🤪')">🤪</span>
                        <span class="emoji-item" onclick="insertEmoji('🤨')">🤨</span>
                        <span class="emoji-item" onclick="insertEmoji('🧐')">🧐</span>
                        <span class="emoji-item" onclick="insertEmoji('🤓')">🤓</span>
                        <span class="emoji-item" onclick="insertEmoji('😎')">😎</span>
                        <span class="emoji-item" onclick="insertEmoji('🤩')">🤩</span>
                        <span class="emoji-item" onclick="insertEmoji('🥳')">🥳</span>
                        <span class="emoji-item" onclick="insertEmoji('😏')">😏</span>
                        <span class="emoji-item" onclick="insertEmoji('😒')">😒</span>
                        <span class="emoji-item" onclick="insertEmoji('😞')">😞</span>
                        <span class="emoji-item" onclick="insertEmoji('😔')">😔</span>
                        <span class="emoji-item" onclick="insertEmoji('😟')">😟</span>
                        <span class="emoji-item" onclick="insertEmoji('😕')">😕</span>
                        <span class="emoji-item" onclick="insertEmoji('🙁')">🙁</span>
                        <span class="emoji-item" onclick="insertEmoji('☹️')">☹️</span>
                        <span class="emoji-item" onclick="insertEmoji('😣')">😣</span>
                        <span class="emoji-item" onclick="insertEmoji('😖')">😖</span>
                        <span class="emoji-item" onclick="insertEmoji('😫')">😫</span>
                        <span class="emoji-item" onclick="insertEmoji('😩')">😩</span>
                        <span class="emoji-item" onclick="insertEmoji('🥺')">🥺</span>
                        <span class="emoji-item" onclick="insertEmoji('😢')">😢</span>
                        <span class="emoji-item" onclick="insertEmoji('😭')">😭</span>
                        <span class="emoji-item" onclick="insertEmoji('😤')">😤</span>
                        <span class="emoji-item" onclick="insertEmoji('😠')">😠</span>
                        <span class="emoji-item" onclick="insertEmoji('😡')">😡</span>
                        <span class="emoji-item" onclick="insertEmoji('🤬')">🤬</span>
                        <span class="emoji-item" onclick="insertEmoji('🤯')">🤯</span>
                        <span class="emoji-item" onclick="insertEmoji('😳')">😳</span>
                        <span class="emoji-item" onclick="insertEmoji('🥵')">🥵</span>
                        <span class="emoji-item" onclick="insertEmoji('🥶')">🥶</span>
                        <span class="emoji-item" onclick="insertEmoji('😱')">😱</span>
                        <span class="emoji-item" onclick="insertEmoji('😨')">😨</span>
                        <span class="emoji-item" onclick="insertEmoji('😰')">😰</span>
                        <span class="emoji-item" onclick="insertEmoji('😥')">😥</span>
                        <span class="emoji-item" onclick="insertEmoji('😓')">😓</span>
                        <span class="emoji-item" onclick="insertEmoji('🤗')">🤗</span>
                        <span class="emoji-item" onclick="insertEmoji('🤔')">🤔</span>
                        <span class="emoji-item" onclick="insertEmoji('🤭')">🤭</span>
                        <span class="emoji-item" onclick="insertEmoji('🤫')">🤫</span>
                        <span class="emoji-item" onclick="insertEmoji('🤥')">🤥</span>
                        <span class="emoji-item" onclick="insertEmoji('😶')">😶</span>
                        <span class="emoji-item" onclick="insertEmoji('😐')">😐</span>
                        <span class="emoji-item" onclick="insertEmoji('😑')">😑</span>
                        <span class="emoji-item" onclick="insertEmoji('😬')">😬</span>
                        <span class="emoji-item" onclick="insertEmoji('🙄')">🙄</span>
                        <span class="emoji-item" onclick="insertEmoji('😯')">😯</span>
                        <span class="emoji-item" onclick="insertEmoji('😦')">😦</span>
                        <span class="emoji-item" onclick="insertEmoji('😧')">😧</span>
                        <span class="emoji-item" onclick="insertEmoji('😮')">😮</span>
                        <span class="emoji-item" onclick="insertEmoji('😲')">😲</span>
                        <span class="emoji-item" onclick="insertEmoji('🥱')">🥱</span>
                        <span class="emoji-item" onclick="insertEmoji('😴')">😴</span>
                        <span class="emoji-item" onclick="insertEmoji('🤤')">🤤</span>
                        <span class="emoji-item" onclick="insertEmoji('😪')">😪</span>
                        <span class="emoji-item" onclick="insertEmoji('😵')">😵</span>
                        <span class="emoji-item" onclick="insertEmoji('🤐')">🤐</span>
                        <span class="emoji-item" onclick="insertEmoji('🥴')">🥴</span>
                        <span class="emoji-item" onclick="insertEmoji('🤢')">🤢</span>
                        <span class="emoji-item" onclick="insertEmoji('🤮')">🤮</span>
                        <span class="emoji-item" onclick="insertEmoji('🤧')">🤧</span>
                        <span class="emoji-item" onclick="insertEmoji('😷')">😷</span>
                        <span class="emoji-item" onclick="insertEmoji('🤒')">🤒</span>
                        <span class="emoji-item" onclick="insertEmoji('🤕')">🤕</span>
                        <span class="emoji-item" onclick="insertEmoji('🤑')">🤑</span>
                        <span class="emoji-item" onclick="insertEmoji('🤠')">🤠</span>
                        <span class="emoji-item" onclick="insertEmoji('😈')">😈</span>
                        <span class="emoji-item" onclick="insertEmoji('👿')">👿</span>
                        <span class="emoji-item" onclick="insertEmoji('👹')">👹</span>
                        <span class="emoji-item" onclick="insertEmoji('👺')">👺</span>
                        <span class="emoji-item" onclick="insertEmoji('🤡')">🤡</span>
                        <span class="emoji-item" onclick="insertEmoji('💩')">💩</span>
                        <span class="emoji-item" onclick="insertEmoji('👻')">👻</span>
                        <span class="emoji-item" onclick="insertEmoji('💀')">💀</span>
                        <span class="emoji-item" onclick="insertEmoji('☠️')">☠️</span>
                        <span class="emoji-item" onclick="insertEmoji('👽')">👽</span>
                        <span class="emoji-item" onclick="insertEmoji('👾')">👾</span>
                        <span class="emoji-item" onclick="insertEmoji('🤖')">🤖</span>
                        <span class="emoji-item" onclick="insertEmoji('🎃')">🎃</span>
                        <span class="emoji-item" onclick="insertEmoji('😺')">😺</span>
                        <span class="emoji-item" onclick="insertEmoji('😸')">😸</span>
                        <span class="emoji-item" onclick="insertEmoji('😹')">😹</span>
                        <span class="emoji-item" onclick="insertEmoji('😻')">😻</span>
                        <span class="emoji-item" onclick="insertEmoji('😼')">😼</span>
                        <span class="emoji-item" onclick="insertEmoji('😽')">😽</span>
                        <span class="emoji-item" onclick="insertEmoji('🙀')">🙀</span>
                        <span class="emoji-item" onclick="insertEmoji('😿')">😿</span>
                        <span class="emoji-item" onclick="insertEmoji('😾')">😾</span>
                        <span class="emoji-item" onclick="insertEmoji('👋')">👋</span>
                        <span class="emoji-item" onclick="insertEmoji('🤚')">🤚</span>
                        <span class="emoji-item" onclick="insertEmoji('🖐️')">🖐️</span>
                        <span class="emoji-item" onclick="insertEmoji('✋')">✋</span>
                        <span class="emoji-item" onclick="insertEmoji('🖖')">🖖</span>
                        <span class="emoji-item" onclick="insertEmoji('👌')">👌</span>
                        <span class="emoji-item" onclick="insertEmoji('🤏')">🤏</span>
                        <span class="emoji-item" onclick="insertEmoji('✌️')">✌️</span>
                        <span class="emoji-item" onclick="insertEmoji('🤞')">🤞</span>
                        <span class="emoji-item" onclick="insertEmoji('🤟')">🤟</span>
                        <span class="emoji-item" onclick="insertEmoji('🤘')">🤘</span>
                        <span class="emoji-item" onclick="insertEmoji('🤙')">🤙</span>
                        <span class="emoji-item" onclick="insertEmoji('👈')">👈</span>
                        <span class="emoji-item" onclick="insertEmoji('👉')">👉</span>
                        <span class="emoji-item" onclick="insertEmoji('👆')">👆</span>
                        <span class="emoji-item" onclick="insertEmoji('🖕')">🖕</span>
                        <span class="emoji-item" onclick="insertEmoji('👇')">👇</span>
                        <span class="emoji-item" onclick="insertEmoji('☝️')">☝️</span>
                        <span class="emoji-item" onclick="insertEmoji('👍')">👍</span>
                        <span class="emoji-item" onclick="insertEmoji('👎')">👎</span>
                        <span class="emoji-item" onclick="insertEmoji('✊')">✊</span>
                        <span class="emoji-item" onclick="insertEmoji('👊')">👊</span>
                        <span class="emoji-item" onclick="insertEmoji('🤛')">🤛</span>
                        <span class="emoji-item" onclick="insertEmoji('🤜')">🤜</span>
                        <span class="emoji-item" onclick="insertEmoji('👏')">👏</span>
                        <span class="emoji-item" onclick="insertEmoji('🙌')">🙌</span>
                        <span class="emoji-item" onclick="insertEmoji('👐')">👐</span>
                        <span class="emoji-item" onclick="insertEmoji('🤲')">🤲</span>
                        <span class="emoji-item" onclick="insertEmoji('🤝')">🤝</span>
                        <span class="emoji-item" onclick="insertEmoji('🙏')">🙏</span>
                        <span class="emoji-item" onclick="insertEmoji('❤️')">❤️</span>
                        <span class="emoji-item" onclick="insertEmoji('🧡')">🧡</span>
                        <span class="emoji-item" onclick="insertEmoji('💛')">💛</span>
                        <span class="emoji-item" onclick="insertEmoji('💚')">💚</span>
                        <span class="emoji-item" onclick="insertEmoji('💙')">💙</span>
                        <span class="emoji-item" onclick="insertEmoji('💜')">💜</span>
                        <span class="emoji-item" onclick="insertEmoji('🖤')">🖤</span>
                        <span class="emoji-item" onclick="insertEmoji('🤍')">🤍</span>
                        <span class="emoji-item" onclick="insertEmoji('🤎')">🤎</span>
                        <span class="emoji-item" onclick="insertEmoji('💔')">💔</span>
                        <span class="emoji-item" onclick="insertEmoji('❣️')">❣️</span>
                        <span class="emoji-item" onclick="insertEmoji('💕')">💕</span>
                        <span class="emoji-item" onclick="insertEmoji('💞')">💞</span>
                        <span class="emoji-item" onclick="insertEmoji('💓')">💓</span>
                        <span class="emoji-item" onclick="insertEmoji('💗')">💗</span>
                        <span class="emoji-item" onclick="insertEmoji('💖')">💖</span>
                        <span class="emoji-item" onclick="insertEmoji('💘')">💘</span>
                        <span class="emoji-item" onclick="insertEmoji('💝')">💝</span>
                        <span class="emoji-item" onclick="insertEmoji('💟')">💟</span>
                        <span class="emoji-item" onclick="insertEmoji('💯')">💯</span>
                        <span class="emoji-item" onclick="insertEmoji('💢')">💢</span>
                        <span class="emoji-item" onclick="insertEmoji('💥')">💥</span>
                        <span class="emoji-item" onclick="insertEmoji('💫')">💫</span>
                        <span class="emoji-item" onclick="insertEmoji('💦')">💦</span>
                        <span class="emoji-item" onclick="insertEmoji('💨')">💨</span>
                        <span class="emoji-item" onclick="insertEmoji('🕳️')">🕳️</span>
                        <span class="emoji-item" onclick="insertEmoji('💬')">💬</span>
                        <span class="emoji-item" onclick="insertEmoji('👁️‍🗨️')">👁️‍🗨️</span>
                        <span class="emoji-item" onclick="insertEmoji('🗨️')">🗨️</span>
                        <span class="emoji-item" onclick="insertEmoji('🗯️')">🗯️</span>
                        <span class="emoji-item" onclick="insertEmoji('💭')">💭</span>
                        <span class="emoji-item" onclick="insertEmoji('💤')">💤</span>
                    </div>
                </div>

                <div class="input-group">
                    <div class="input-tools">
                        <button class="tool-btn" onclick="toggleEmojiPanel()" title="表情">
                            😀
                        </button>
                        <button class="tool-btn" onclick="triggerImageUpload()" title="上传图片">
                            📷
                        </button>
                        <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                    </div>
                    <textarea class="input-text" id="messageInput" placeholder="请输入您的问题..." rows="1"></textarea>
                    <div class="input-actions">
                        <button class="action-btn" id="sendBtn" onclick="sendMessage()">
                            <span>发送</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/js/layui/layui.js"></script>
<script>
    // 全局变量
    let isLoading = false;
    let lastMessageTime = 0;
    let messageCheckInterval = null;
    let lastMessageId = 0;
    let isPageVisible = true;
    let layer; // layui layer 对象

    // 全局发送消息函数
    window.sendMessage = function() {
        const messageText = $('#messageInput').val().trim();
        const hasImage = window.pendingImageFile;

        if (!messageText && !hasImage) {
            if (layer) {
                layer.msg('请输入消息内容或选择图片');
            } else {
                alert('请输入消息内容或选择图片');
            }
            return;
        }

        if (messageText && messageText.length > 500) {
            if (layer) {
                layer.msg('消息内容不能超过500字');
            } else {
                alert('消息内容不能超过500字');
            }
            return;
        }

        // 防止频繁发送
        let now = Date.now();
        if (now - lastMessageTime < 1000) {
            if (layer) {
                layer.msg('发送太频繁，请稍后再试');
            } else {
                alert('发送太频繁，请稍后再试');
            }
            return;
        }
        lastMessageTime = now;

        $('#sendBtn').prop('disabled', true);

        // 如果有图片，先上传图片
        if (hasImage) {
            uploadImageAndSend(messageText);
        } else {
            // 只发送文字消息
            sendTextMessage(messageText);
        }
    };

    // 上传图片并发送消息
    function uploadImageAndSend(messageText) {
        const formData = new FormData();
        formData.append('image', window.pendingImageFile);
        formData.append('message', messageText);
        formData.append('type', 'image');

        // 显示上传状态
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }

        $.ajax({
            url: 'customer.php?type=upload_image',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                if (typeof res === 'string') {
                    res = JSON.parse(res);
                }

                if (res.code === 1) {
                    // 发送包含图片的消息
                    sendImageMessage(messageText, res.data.image_url);
                } else {
                    if (layer) {
                        layer.msg(res.msg || '图片上传失败');
                    } else {
                        alert(res.msg || '图片上传失败');
                    }
                    $('#sendBtn').prop('disabled', false);
                }
            },
            error: function() {
                if (layer) {
                    layer.msg('图片上传失败，请重试');
                } else {
                    alert('图片上传失败，请重试');
                }
                $('#sendBtn').prop('disabled', false);
            }
        });
    }

    // 发送图片消息
    function sendImageMessage(messageText, imageUrl) {
        $.post('customer.php?type=send_message', {
            message: messageText,
            message_type: 'image',
            image_url: imageUrl
        }, function(res) {
            if (typeof res === 'string') {
                res = JSON.parse(res);
            }

            if (res.code === 1) {
                // 添加消息到界面
                let messageHtml = renderMessage(res.data);
                $('#chatMessages').append(messageHtml);
                $('#messageInput').val('');
                removeImagePreview();
                scrollToBottom();

                // 自动回复提示
                setTimeout(function() {
                    addSystemMessage('您的消息已发送，客服将尽快回复您！');
                }, 1000);
            } else {
                if (layer) {
                    layer.msg(res.msg || '发送失败');
                } else {
                    alert(res.msg || '发送失败');
                }
            }

            $('#sendBtn').prop('disabled', false);
        }).fail(function() {
            if (layer) {
                layer.msg('网络错误，发送失败');
            } else {
                alert('网络错误，发送失败');
            }
            $('#sendBtn').prop('disabled', false);
        });
    }

    // 发送文字消息
    function sendTextMessage(messageText) {
        $.post('customer.php?type=send_message', {
            message: messageText,
            message_type: 'text'
        }, function(res) {
            if (typeof res === 'string') {
                res = JSON.parse(res);
            }

            if (res.code === 1) {
                // 添加消息到界面
                let messageHtml = renderMessage(res.data);
                $('#chatMessages').append(messageHtml);
                $('#messageInput').val('');
                scrollToBottom();

                // 自动回复提示
                setTimeout(function() {
                    addSystemMessage('您的消息已发送，客服将尽快回复您！');
                }, 1000);
            } else {
                if (layer) {
                    layer.msg(res.msg || '发送失败');
                } else {
                    alert(res.msg || '发送失败');
                }
            }

            $('#sendBtn').prop('disabled', false);
        }).fail(function() {
            if (layer) {
                layer.msg('网络错误，发送失败');
            } else {
                alert('网络错误，发送失败');
            }
            $('#sendBtn').prop('disabled', false);
        });
    }

    layui.use(['layer'], function(){
        layer = layui.layer;

        // 加载消息
        function loadMessages() {
            if (isLoading) return;
            isLoading = true;

            $.get('customer.php?type=get_messages', function(res) {
                if (typeof res === 'string') {
                    res = JSON.parse(res);
                }

                if (res.code === 1) {
                    renderMessages(res.data);
                } else {
                    $('#chatMessages').html('<div class="no-messages">加载失败</div>');
                }
                isLoading = false;
            }).fail(function() {
                $('#chatMessages').html('<div class="no-messages">网络错误</div>');
                isLoading = false;
            });
        }

        // 渲染消息列表
        function renderMessages(messages) {
            if (messages.length === 0) {
                $('#chatMessages').html('<div class="no-messages">暂无消息，开始对话吧！</div>');
                return;
            }

            let html = '';
            messages.reverse().forEach(function(message) {
                html += renderMessage(message);
                // 更新最后消息ID
                if (message.id > lastMessageId) {
                    lastMessageId = message.id;
                }
            });

            $('#chatMessages').html(html);
            scrollToBottom();
        }

        // 渲染单条消息
        function renderMessage(message) {
            let senderClass = message.sender_type === 'user' ? 'user' : 'admin';
            let avatar = message.sender_type === 'user' ? '用' : '客';
            let time = formatTime(message.tjtime);

            let content = '';
            if (message.message_type === 'image' && message.image_url) {
                // 图片消息
                content = `
                    ${message.message ? `<div style="margin-bottom: 8px;">${escapeHtml(message.message)}</div>` : ''}
                    <img src="${message.image_url}" class="message-image" onclick="previewImage('${message.image_url}')" alt="图片">
                `;
            } else {
                // 文字消息
                content = escapeHtml(message.message);
            }

            return `
                <div class="message ${senderClass}">
                    <div class="message-avatar">${avatar}</div>
                    <div>
                        <div class="message-content">${content}</div>
                        <div class="message-time">${time}</div>
                    </div>
                </div>
            `;
        }

        // 图片预览功能
        window.previewImage = function(imageUrl) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                cursor: pointer;
            `;

            const img = document.createElement('img');
            img.src = imageUrl;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            `;

            modal.appendChild(img);
            document.body.appendChild(modal);

            modal.onclick = function() {
                document.body.removeChild(modal);
            };
        };

        // 原始发送消息函数已被重新定义以支持图片功能

        // 发送快捷回复
        window.sendQuickReply = function(text) {
            $('#messageInput').val(text);
            sendMessage();
        };

        // 添加系统消息
        function addSystemMessage(text) {
            let time = formatTime(new Date().toISOString().slice(0, 19).replace('T', ' '));
            let messageHtml = `
                <div class="message admin">
                    <div class="message-avatar">客服</div>
                    <div>
                        <div class="message-content">${text}</div>
                        <div class="message-time">${time}</div>
                    </div>
                </div>
            `;
            $('#chatMessages').append(messageHtml);
            scrollToBottom();
        }

        // 滚动到底部
        function scrollToBottom() {
            let chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 检查新消息
        function checkNewMessages() {
            if (isLoading) return;

            $.get('customer.php?type=check_new_messages&last_id=' + lastMessageId, function(res) {
                if (typeof res === 'string') {
                    res = JSON.parse(res);
                }

                if (res.code === 1 && res.data && res.data.length > 0) {
                    // 有新消息
                    res.data.forEach(function(message) {
                        if (message.sender_type === 'admin') {
                            // 客服回复，播放提示音
                            playNotificationSound();
                            // 显示桌面通知
                            showDesktopNotification('客服回复', message.message);
                        }

                        // 添加消息到界面
                        let messageHtml = renderMessage(message);
                        $('#chatMessages').append(messageHtml);

                        // 更新最后消息ID
                        if (message.id > lastMessageId) {
                            lastMessageId = message.id;
                        }
                    });

                    scrollToBottom();
                }
            }).fail(function() {
                // 静默处理错误
            });
        }

        // 播放通知音
        function playNotificationSound() {
            try {
                let audio = new Audio('/js/dd.mp3');
                audio.volume = 0.7; // 设置音量为70%
                audio.play().catch(function() {
                    // 如果播放失败，尝试备用提示音
                    try {
                        let fallbackAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                        fallbackAudio.play();
                    } catch (e) {
                        // 忽略错误
                    }
                });
            } catch (e) {
                // 忽略错误
            }
        }

        // 显示桌面通知
        function showDesktopNotification(title, message) {
            if (!isPageVisible && 'Notification' in window) {
                if (Notification.permission === 'granted') {
                    new Notification(title, {
                        body: message.length > 50 ? message.substring(0, 50) + '...' : message,
                        icon: '/favicon.ico'
                    });
                } else if (Notification.permission !== 'denied') {
                    Notification.requestPermission().then(function(permission) {
                        if (permission === 'granted') {
                            new Notification(title, {
                                body: message.length > 50 ? message.substring(0, 50) + '...' : message,
                                icon: '/favicon.ico'
                            });
                        }
                    });
                }
            }
        }

        // 开始消息检查
        function startMessageCheck() {
            if (messageCheckInterval) {
                clearInterval(messageCheckInterval);
            }
            messageCheckInterval = setInterval(checkNewMessages, 3000); // 每3秒检查一次
        }

        // 停止消息检查
        function stopMessageCheck() {
            if (messageCheckInterval) {
                clearInterval(messageCheckInterval);
                messageCheckInterval = null;
            }
        }

        // 格式化时间
        function formatTime(timeStr) {
            let date = new Date(timeStr);
            let now = new Date();
            let today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            let messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

            if (messageDate.getTime() === today.getTime()) {
                return date.toTimeString().slice(0, 5);
            } else {
                return date.toLocaleDateString() + ' ' + date.toTimeString().slice(0, 5);
            }
        }

        // HTML转义
        function escapeHtml(text) {
            let div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 显示图片
        window.showImage = function(url) {
            layer.photos({
                photos: {
                    data: [{
                        alt: '聊天图片',
                        src: url
                    }]
                },
                anim: 5
            });
        };

        // 复制文本
        window.copyText = function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    layer.msg('复制成功');
                }).catch(function() {
                    fallbackCopy(text);
                });
            } else {
                fallbackCopy(text);
            }
        };

        // 兼容性复制
        function fallbackCopy(text) {
            let textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                layer.msg('复制成功');
            } catch (err) {
                layer.msg('复制失败，请手动复制');
            }
            document.body.removeChild(textArea);
        }

        // 输入框事件
        $('#messageInput').on('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 自动调整输入框高度
        $('#messageInput').on('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            isPageVisible = !document.hidden;
            if (isPageVisible) {
                // 页面变为可见时，立即检查新消息
                checkNewMessages();
                startMessageCheck();
            } else {
                // 页面隐藏时，继续检查消息以便发送通知
                // 不停止检查，这样可以在后台收到消息时发送通知
            }
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            stopMessageCheck();
        });

        // 请求通知权限
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // 联系方式展开/收起
        window.toggleContactMethods = function() {
            const methods = document.getElementById('contactMethods');
            const toggle = document.getElementById('contactToggle');

            if (methods.style.display === 'none') {
                methods.style.display = 'block';
                toggle.classList.add('expanded');
            } else {
                methods.style.display = 'none';
                toggle.classList.remove('expanded');
            }
        };

        // 表情包面板切换
        window.toggleEmojiPanel = function() {
            const panel = document.getElementById('emojiPanel');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
            } else {
                panel.style.display = 'none';
            }
        };

        // 插入表情
        window.insertEmoji = function(emoji) {
            const input = document.getElementById('messageInput');
            const cursorPos = input.selectionStart;
            const textBefore = input.value.substring(0, cursorPos);
            const textAfter = input.value.substring(cursorPos);

            input.value = textBefore + emoji + textAfter;
            input.focus();
            input.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

            // 关闭表情面板
            document.getElementById('emojiPanel').style.display = 'none';
        };

        // 触发图片上传
        window.triggerImageUpload = function() {
            document.getElementById('imageInput').click();
        };

        // 处理图片上传
        window.handleImageUpload = function(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 检查文件类型
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件！');
                return;
            }

            // 检查文件大小（限制5MB）
            if (file.size > 5 * 1024 * 1024) {
                alert('图片大小不能超过5MB！');
                return;
            }

            // 显示上传预览
            const reader = new FileReader();
            reader.onload = function(e) {
                showImagePreview(e.target.result, file);
            };
            reader.readAsDataURL(file);
        };

        // 显示图片预览
        function showImagePreview(dataUrl, file) {
            const previewHtml = `
                <div class="image-upload-preview" id="imagePreview">
                    <img src="${dataUrl}" class="image-preview" alt="预览图片">
                    <button class="image-remove" onclick="removeImagePreview()">×</button>
                    <div class="loading-overlay" style="display: none;">
                        <span>上传中...</span>
                    </div>
                </div>
            `;

            // 在输入框上方显示预览
            const inputGroup = document.querySelector('.input-group');
            inputGroup.insertAdjacentHTML('beforebegin', previewHtml);

            // 存储文件数据
            window.pendingImageFile = file;
            window.pendingImageData = dataUrl;
        }

        // 移除图片预览
        window.removeImagePreview = function() {
            const preview = document.getElementById('imagePreview');
            if (preview) {
                preview.remove();
            }
            window.pendingImageFile = null;
            window.pendingImageData = null;
            document.getElementById('imageInput').value = '';
        };

        // sendMessage 函数已在全局作用域定义

        // 上传图片并发送消息
        function uploadImageAndSend(messageText) {
            const formData = new FormData();
            formData.append('image', window.pendingImageFile);
            formData.append('message', messageText);
            formData.append('type', 'image');

            // 显示上传状态
            const loadingOverlay = document.querySelector('.loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }

            $.ajax({
                url: 'customer.php?type=upload_image',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(res) {
                    if (typeof res === 'string') {
                        res = JSON.parse(res);
                    }

                    if (res.code === 1) {
                        // 发送包含图片的消息
                        sendImageMessage(messageText, res.data.image_url);
                    } else {
                        layer.msg(res.msg || '图片上传失败');
                        $('#sendBtn').prop('disabled', false);
                    }
                },
                error: function() {
                    layer.msg('图片上传失败，请重试');
                    $('#sendBtn').prop('disabled', false);
                }
            });
        }

        // 发送图片消息
        function sendImageMessage(messageText, imageUrl) {
            $.post('customer.php?type=send_message', {
                message: messageText,
                message_type: 'image',
                image_url: imageUrl
            }, function(res) {
                if (typeof res === 'string') {
                    res = JSON.parse(res);
                }

                if (res.code === 1) {
                    // 添加消息到界面
                    let messageHtml = renderMessage(res.data);
                    $('#chatMessages').append(messageHtml);
                    $('#messageInput').val('');
                    removeImagePreview();
                    scrollToBottom();

                    // 自动回复提示
                    setTimeout(function() {
                        addSystemMessage('您的消息已发送，客服将尽快回复您！');
                    }, 1000);
                } else {
                    layer.msg(res.msg || '发送失败');
                }

                $('#sendBtn').prop('disabled', false);
            }).fail(function() {
                layer.msg('网络错误，发送失败');
                $('#sendBtn').prop('disabled', false);
            });
        }

        // 发送文字消息
        function sendTextMessage(messageText) {
            $.post('customer.php?type=send_message', {
                message: messageText,
                message_type: 'text'
            }, function(res) {
                if (typeof res === 'string') {
                    res = JSON.parse(res);
                }

                if (res.code === 1) {
                    // 添加消息到界面
                    let messageHtml = renderMessage(res.data);
                    $('#chatMessages').append(messageHtml);
                    $('#messageInput').val('');
                    scrollToBottom();

                    // 自动回复提示
                    setTimeout(function() {
                        addSystemMessage('您的消息已发送，客服将尽快回复您！');
                    }, 1000);
                } else {
                    layer.msg(res.msg || '发送失败');
                }

                $('#sendBtn').prop('disabled', false);
            }).fail(function() {
                layer.msg('网络错误，发送失败');
                $('#sendBtn').prop('disabled', false);
            });
        }

        // 复制文本功能已在上面定义

        // 点击空白处关闭表情面板
        document.addEventListener('click', function(e) {
            const emojiPanel = document.getElementById('emojiPanel');
            const emojiBtn = e.target.closest('.tool-btn');

            if (emojiPanel && emojiPanel.style.display === 'block' && !emojiPanel.contains(e.target) && !emojiBtn) {
                emojiPanel.style.display = 'none';
            }
        });

        // 初始化
        loadMessages();
        startMessageCheck();
    });
</script>
</body>
</html>
